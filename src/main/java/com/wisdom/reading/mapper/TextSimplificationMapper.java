package com.wisdom.reading.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wisdom.reading.entity.TextSimplification;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文本简化Mapper接口
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Mapper
public interface TextSimplificationMapper extends BaseMapper<TextSimplification> {

    /**
     * 根据用户ID查询简化记录
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 简化记录列表
     */
    @Select("SELECT * FROM text_simplifications WHERE user_id = #{userId} AND deleted = 0 ORDER BY create_time DESC LIMIT #{limit}")
    List<TextSimplification> findByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 根据分析ID查询简化记录
     * 
     * @param analysisId 分析ID
     * @return 简化记录列表
     */
    @Select("SELECT * FROM text_simplifications WHERE analysis_id = #{analysisId} AND deleted = 0 ORDER BY create_time DESC")
    List<TextSimplification> findByAnalysisId(@Param("analysisId") Long analysisId);

    /**
     * 根据目标年级查询简化记录
     * 
     * @param targetGrade 目标年级
     * @param limit 限制数量
     * @return 简化记录列表
     */
    @Select("SELECT * FROM text_simplifications WHERE target_grade = #{targetGrade} AND deleted = 0 ORDER BY create_time DESC LIMIT #{limit}")
    List<TextSimplification> findByTargetGrade(@Param("targetGrade") String targetGrade, @Param("limit") Integer limit);

    /**
     * 查询用户在指定时间范围内的简化记录
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 简化记录列表
     */
    @Select("SELECT * FROM text_simplifications WHERE user_id = #{userId} AND create_time BETWEEN #{startTime} AND #{endTime} AND deleted = 0 ORDER BY create_time DESC")
    List<TextSimplification> findByUserIdAndTimeRange(@Param("userId") Long userId, 
                                                     @Param("startTime") LocalDateTime startTime, 
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 统计用户简化数据
     * 
     * @param userId 用户ID
     * @return 简化统计
     */
    SimplificationStats getSimplificationStats(@Param("userId") Long userId);

    /**
     * 获取系统简化统计
     * 
     * @return 系统简化统计
     */
    SystemSimplificationStats getSystemSimplificationStats();

    /**
     * 获取简化质量分布
     * 
     * @param userId 用户ID（可选）
     * @return 质量分布
     */
    List<QualityDistribution> getQualityDistribution(@Param("userId") Long userId);

    /**
     * 获取目标年级分布
     * 
     * @param userId 用户ID（可选）
     * @return 年级分布
     */
    List<GradeDistribution> getTargetGradeDistribution(@Param("userId") Long userId);

    /**
     * 简化统计信息
     */
    class SimplificationStats {
        private Long totalCount;
        private Long todayCount;
        private Long weekCount;
        private Long monthCount;
        private Double avgQualityScore;
        private Double avgSimplificationRatio;
        private String mostFrequentTargetGrade;
        
        // 构造函数、getter和setter省略
    }

    /**
     * 系统简化统计
     */
    class SystemSimplificationStats {
        private Long totalSimplifications;
        private Long totalUsers;
        private Long todaySimplifications;
        private Double avgProcessingTime;
        private Double successRate;
        private Double avgQualityScore;
        
        // 构造函数、getter和setter省略
    }

    /**
     * 质量分布
     */
    class QualityDistribution {
        private String qualityRange;
        private Long count;
        private Double percentage;
        
        // 构造函数、getter和setter省略
    }

    /**
     * 年级分布
     */
    class GradeDistribution {
        private String targetGrade;
        private Long count;
        private Double percentage;
        
        // 构造函数、getter和setter省略
    }
}
