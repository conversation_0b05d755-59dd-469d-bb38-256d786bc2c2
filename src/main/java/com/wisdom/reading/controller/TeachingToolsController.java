package com.wisdom.reading.controller;

import com.wisdom.reading.dto.*;
import com.wisdom.reading.service.TeachingToolsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 教学工具控制器
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Tag(name = "教学工具", description = "教学资源生成和文本推荐相关接口")
@RestController
@RequestMapping("/teaching-tools")
@RequiredArgsConstructor
public class TeachingToolsController {

    private final TeachingToolsService teachingToolsService;

    @Operation(summary = "生成教学资源", description = "根据文本分析结果生成教学资源")
    @PostMapping("/generate-resources")
    @PreAuthorize("hasRole('TEACHER') or hasRole('ADMIN')")
    public ResponseEntity<TeachingResourceResponse> generateTeachingResources(
            @Valid @RequestBody TeachingResourceRequest request) {
        
        TeachingResourceResponse response = teachingToolsService.generateTeachingResources(request);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "获取推荐文本", description = "根据条件推荐合适的阅读文本")
    @PostMapping("/recommend-texts")
    public ResponseEntity<List<TextRecommendationResponse>> recommendTexts(
            @Valid @RequestBody TextRecommendationRequest request) {
        
        List<TextRecommendationResponse> responses = teachingToolsService.recommendTexts(request);
        return ResponseEntity.ok(responses);
    }

    @Operation(summary = "获取同级文本", description = "根据分级结果获取同等难度的文本")
    @GetMapping("/similar-texts")
    public ResponseEntity<List<TextRecommendationResponse>> getSimilarTexts(
            @Parameter(description = "分析ID") @RequestParam Long analysisId,
            @Parameter(description = "推荐数量") @RequestParam(defaultValue = "10") Integer count) {
        
        List<TextRecommendationResponse> responses = teachingToolsService.getSimilarTexts(analysisId, count);
        return ResponseEntity.ok(responses);
    }

    @Operation(summary = "生成生词卡片", description = "根据文本生成生词学习卡片")
    @PostMapping("/vocabulary-cards")
    public ResponseEntity<VocabularyCardsResponse> generateVocabularyCards(
            @Parameter(description = "分析ID") @RequestParam Long analysisId,
            @Parameter(description = "目标年级") @RequestParam(required = false) String targetGrade) {
        
        VocabularyCardsResponse response = teachingToolsService.generateVocabularyCards(analysisId, targetGrade);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "生成阅读理解题", description = "根据文本生成阅读理解练习题")
    @PostMapping("/comprehension-questions")
    public ResponseEntity<ComprehensionQuestionsResponse> generateComprehensionQuestions(
            @Parameter(description = "分析ID") @RequestParam Long analysisId,
            @Parameter(description = "题目数量") @RequestParam(defaultValue = "5") Integer questionCount,
            @Parameter(description = "难度等级") @RequestParam(defaultValue = "MEDIUM") String difficulty) {
        
        ComprehensionQuestionsResponse response = teachingToolsService.generateComprehensionQuestions(
                analysisId, questionCount, difficulty);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "生成阅读指导", description = "根据文本生成阅读指导建议")
    @PostMapping("/reading-guide")
    public ResponseEntity<ReadingGuideResponse> generateReadingGuide(
            @Parameter(description = "分析ID") @RequestParam Long analysisId,
            @Parameter(description = "学生水平") @RequestParam(defaultValue = "BEGINNER") String studentLevel) {
        
        ReadingGuideResponse response = teachingToolsService.generateReadingGuide(analysisId, studentLevel);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "获取教学资源历史", description = "获取用户的教学资源生成历史")
    @GetMapping("/resources/history")
    @PreAuthorize("hasRole('TEACHER') or hasRole('ADMIN')")
    public ResponseEntity<List<TeachingResourceResponse>> getResourceHistory(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "资源类型") @RequestParam(required = false) String resourceType) {
        
        List<TeachingResourceResponse> responses = teachingToolsService.getResourceHistory(page, size, resourceType);
        return ResponseEntity.ok(responses);
    }

    @Operation(summary = "导出教学资源", description = "导出教学资源为文档")
    @GetMapping("/resources/{resourceId}/export")
    @PreAuthorize("hasRole('TEACHER') or hasRole('ADMIN')")
    public ResponseEntity<byte[]> exportTeachingResource(
            @Parameter(description = "资源ID") @PathVariable Long resourceId,
            @Parameter(description = "导出格式") @RequestParam(defaultValue = "PDF") String format) {
        
        byte[] data = teachingToolsService.exportTeachingResource(resourceId, format);
        return ResponseEntity.ok()
                .header("Content-Disposition", "attachment; filename=teaching-resource." + format.toLowerCase())
                .body(data);
    }

    @Operation(summary = "分享教学资源", description = "分享教学资源给其他用户")
    @PostMapping("/resources/{resourceId}/share")
    @PreAuthorize("hasRole('TEACHER') or hasRole('ADMIN')")
    public ResponseEntity<Void> shareTeachingResource(
            @Parameter(description = "资源ID") @PathVariable Long resourceId,
            @RequestBody ShareResourceRequest request) {
        
        teachingToolsService.shareTeachingResource(resourceId, request);
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "评价教学资源", description = "对教学资源进行评价")
    @PostMapping("/resources/{resourceId}/rate")
    public ResponseEntity<Void> rateTeachingResource(
            @Parameter(description = "资源ID") @PathVariable Long resourceId,
            @RequestBody RateResourceRequest request) {
        
        teachingToolsService.rateTeachingResource(resourceId, request);
        return ResponseEntity.ok().build();
    }



    /**
     * 生词卡片
     */
    public static class VocabularyCard {
        private String word;
        private String pronunciation;
        private String meaning;
        private String partOfSpeech;
        private String example;
        private String imageUrl;
        private String difficultyLevel;
        
        // 构造函数、getter和setter省略
    }

    /**
     * 阅读理解题响应
     */
    public static class ComprehensionQuestionsResponse {
        private Long resourceId;
        private List<ComprehensionQuestion> questions;
        private String difficulty;
        private Integer totalCount;
        
        // 构造函数、getter和setter省略
    }

    /**
     * 阅读理解题
     */
    public static class ComprehensionQuestion {
        private String type; // MULTIPLE_CHOICE, TRUE_FALSE, SHORT_ANSWER, ESSAY
        private String question;
        private List<String> options;
        private String correctAnswer;
        private String explanation;
        private Integer points;
        
        // 构造函数、getter和setter省略
    }

    /**
     * 阅读指导响应
     */
    public static class ReadingGuideResponse {
        private Long resourceId;
        private ReadingGuide guide;
        private String studentLevel;
        
        // 构造函数、getter和setter省略
    }

    /**
     * 阅读指导
     */
    public static class ReadingGuide {
        private List<String> preparationSteps;
        private List<String> readingStrategies;
        private List<String> discussionPoints;
        private List<String> extensionActivities;
        private String timeEstimate;
        
        // 构造函数、getter和setter省略
    }

    /**
     * 分享资源请求
     */
    public static class ShareResourceRequest {
        private List<Long> userIds;
        private String message;
        private Boolean allowEdit;
        
        // 构造函数、getter和setter省略
    }

    /**
     * 评价资源请求
     */
    public static class RateResourceRequest {
        private Integer rating; // 1-5
        private String comment;
        
        // 构造函数、getter和setter省略
    }
}
