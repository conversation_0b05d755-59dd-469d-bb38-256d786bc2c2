package com.wisdom.reading.controller;

import com.wisdom.reading.dto.TextAnalysisRequest;
import com.wisdom.reading.dto.TextAnalysisResponse;
import com.wisdom.reading.service.TextAnalysisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * 文本分析控制器
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Tag(name = "文本分析", description = "文本难度分级和分析相关接口")
@RestController
@RequestMapping("/text-analysis")
@RequiredArgsConstructor
public class TextAnalysisController {

    private final TextAnalysisService textAnalysisService;

    @Operation(summary = "文本分级分析", description = "对输入的文本进行难度分级和详细分析")
    @PostMapping("/analyze")
    public ResponseEntity<TextAnalysisResponse> analyzeText(
            @Valid @RequestBody TextAnalysisRequest request) {
        
        TextAnalysisResponse response = textAnalysisService.analyzeText(request);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "文件上传分析", description = "上传文件进行文本分析")
    @PostMapping("/analyze-file")
    public ResponseEntity<TextAnalysisResponse> analyzeFile(
            @Parameter(description = "文本文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "文件标题") @RequestParam(required = false) String title,
            @Parameter(description = "是否详细分析") @RequestParam(defaultValue = "true") Boolean detailedAnalysis) {
        
        TextAnalysisResponse response = textAnalysisService.analyzeFile(file, title, detailedAnalysis);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "批量文本分析", description = "批量分析多个文本")
    @PostMapping("/batch-analyze")
    public ResponseEntity<List<TextAnalysisResponse>> batchAnalyze(
            @Valid @RequestBody List<TextAnalysisRequest> requests) {
        
        List<TextAnalysisResponse> responses = textAnalysisService.batchAnalyze(requests);
        return ResponseEntity.ok(responses);
    }

    @Operation(summary = "获取分析历史", description = "获取用户的文本分析历史记录")
    @GetMapping("/history")
    public ResponseEntity<List<TextAnalysisResponse>> getAnalysisHistory(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "分级筛选") @RequestParam(required = false) String gradeLevel) {
        
        List<TextAnalysisResponse> history = textAnalysisService.getAnalysisHistory(page, size, gradeLevel);
        return ResponseEntity.ok(history);
    }

    @Operation(summary = "获取分析详情", description = "根据分析ID获取详细分析结果")
    @GetMapping("/{analysisId}")
    public ResponseEntity<TextAnalysisResponse> getAnalysisDetail(
            @Parameter(description = "分析ID") @PathVariable Long analysisId) {
        
        TextAnalysisResponse response = textAnalysisService.getAnalysisDetail(analysisId);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "删除分析记录", description = "删除指定的分析记录")
    @DeleteMapping("/{analysisId}")
    public ResponseEntity<Void> deleteAnalysis(
            @Parameter(description = "分析ID") @PathVariable Long analysisId) {
        
        textAnalysisService.deleteAnalysis(analysisId);
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "获取分级标准", description = "获取系统支持的分级标准信息")
    @GetMapping("/grade-standards")
    public ResponseEntity<List<GradeStandard>> getGradeStandards() {
        
        List<GradeStandard> standards = textAnalysisService.getGradeStandards();
        return ResponseEntity.ok(standards);
    }

    /**
     * 分级标准信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class GradeStandard {
        private String code;
        private String name;
        private String description;
        private String color;
        private Integer minAge;
        private Integer maxAge;
    }
}
