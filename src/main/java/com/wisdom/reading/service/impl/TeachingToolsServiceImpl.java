package com.wisdom.reading.service.impl;

import com.wisdom.reading.controller.TeachingToolsController.*;
import com.wisdom.reading.dto.TeachingResourceRequest;
import com.wisdom.reading.dto.TeachingResourceResponse;
import com.wisdom.reading.dto.TextRecommendationRequest;
import com.wisdom.reading.dto.TextRecommendationResponse;
import com.wisdom.reading.entity.TextAnalysis;
import com.wisdom.reading.entity.TeachingResource;
import com.wisdom.reading.mapper.TextAnalysisMapper;
import com.wisdom.reading.mapper.TeachingResourceMapper;
import com.wisdom.reading.service.TeachingToolsService;
import com.wisdom.reading.service.AIAnalysisService;
import com.wisdom.reading.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 教学工具服务实现类
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TeachingToolsServiceImpl implements TeachingToolsService {

    private final TextAnalysisMapper textAnalysisMapper;
    private final TeachingResourceMapper teachingResourceMapper;
    private final AIAnalysisService aiAnalysisService;

    @Override
    @Transactional
    public TeachingResourceResponse generateTeachingResources(TeachingResourceRequest request) {
        log.info("生成教学资源，分析ID: {}, 资源类型: {}", request.getAnalysisId(), request.getResourceTypes());
        
        try {
            // 1. 获取文本分析结果
            TextAnalysis analysis = textAnalysisMapper.selectById(request.getAnalysisId());
            if (analysis == null) {
                throw new RuntimeException("文本分析记录不存在");
            }
            
            // 2. 调用AI服务生成教学资源
            AIAnalysisService.TeachingResources aiResources = aiAnalysisService.generateTeachingResources(
                    analysis.getOriginalText(), 
                    analysis.getGradeLevel(), 
                    request.getResourceTypes(), 
                    request.getStudentLevel()
            );
            
            // 3. 保存教学资源到数据库
            TeachingResource resource = new TeachingResource();
            resource.setAnalysisId(request.getAnalysisId());
            resource.setUserId(getCurrentUserId());
            resource.setResourceType(String.join(",", request.getResourceTypes()));
            resource.setTitle("教学资源 - " + (analysis.getTitle() != null ? analysis.getTitle() : "未命名文本"));
            resource.setContent(convertToJson(aiResources)); // 将AI生成的资源转换为JSON存储
            resource.setGradeLevel(request.getTargetGrade() != null ? request.getTargetGrade() : analysis.getGradeLevel());
            resource.setDifficultyLevel("MEDIUM");
            resource.setCreateTime(LocalDateTime.now());
            
            teachingResourceMapper.insert(resource);
            
            // 4. 构建响应
            TeachingResourceResponse response = new TeachingResourceResponse();
            response.setResourceId(resource.getId());
            response.setAnalysisId(request.getAnalysisId());
            response.setTitle(resource.getTitle());
            response.setResourceType(resource.getResourceType());
            response.setGradeLevel(resource.getGradeLevel());
            response.setDifficultyLevel(resource.getDifficultyLevel());
            response.setCreateTime(resource.getCreateTime());
            response.setUsageCount(0);
            
            // TODO: 将AI生成的资源转换为响应格式
            convertAIResourcesToResponse(aiResources, response);
            
            log.info("教学资源生成成功，资源ID: {}", resource.getId());
            return response;
            
        } catch (Exception e) {
            log.error("教学资源生成失败", e);
            throw new RuntimeException("教学资源生成失败: " + e.getMessage());
        }
    }

    @Override
    public List<TextRecommendationResponse> recommendTexts(TextRecommendationRequest request) {
        log.info("推荐文本，用户年级: {}, 兴趣: {}", request.getUserGrade(), request.getInterests());
        
        try {
            // 调用AI服务获取推荐文本
            List<AIAnalysisService.RecommendedText> aiRecommendations = aiAnalysisService.recommendTexts(
                    request.getUserGrade(),
                    request.getInterests(),
                    request.getTextType(),
                    request.getDifficulty(),
                    request.getLength(),
                    request.getCount()
            );
            
            // 转换为响应格式
            return aiRecommendations.stream()
                    .map(this::convertToRecommendationResponse)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("文本推荐失败", e);
            throw new RuntimeException("文本推荐失败: " + e.getMessage());
        }
    }

    @Override
    public List<TextRecommendationResponse> getSimilarTexts(Long analysisId, Integer count) {
        log.info("获取同级文本，分析ID: {}, 数量: {}", analysisId, count);
        
        TextAnalysis analysis = textAnalysisMapper.selectById(analysisId);
        if (analysis == null) {
            throw new RuntimeException("文本分析记录不存在");
        }
        
        // 基于分析结果构建推荐请求
        TextRecommendationRequest request = new TextRecommendationRequest();
        request.setUserGrade(analysis.getGradeLevel());
        request.setTextType(analysis.getTopicCategory());
        request.setCount(count);
        request.setBaseAnalysisId(analysisId);
        
        return recommendTexts(request);
    }

    @Override
    public VocabularyCardsResponse generateVocabularyCards(Long analysisId, String targetGrade) {
        log.info("生成生词卡片，分析ID: {}", analysisId);
        
        // TODO: 实现生词卡片生成逻辑
        VocabularyCardsResponse response = new VocabularyCardsResponse();
        response.setResourceId(System.currentTimeMillis()); // 临时ID
        response.setTargetGrade(targetGrade);
        response.setTotalCount(0);
        response.setCards(Arrays.asList());
        
        return response;
    }

    @Override
    public void rateTeachingResource(Long resourceId, RateResourceRequest request) {
        log.info("评价教学资源，资源ID: {}, 评分: {}", resourceId, request.getRating());

        // TODO: 实现资源评价逻辑
        // 1. 验证资源是否存在
        // 2. 保存评价记录
        // 3. 更新资源平均评分
    }

    @Override
    public TeachingResourceResponse getTeachingResourceDetail(Long resourceId) {
        log.info("获取教学资源详情，资源ID: {}", resourceId);

        TeachingResource resource = teachingResourceMapper.selectById(resourceId);
        if (resource == null) {
            throw new RuntimeException("教学资源不存在");
        }

        // TODO: 将实体转换为响应DTO
        return convertToResourceResponse(resource);
    }

    @Override
    public List<TeachingResourceResponse> getUserTeachingResources(Integer page, Integer size, String resourceType) {
        log.info("获取用户教学资源，页码: {}, 大小: {}, 类型: {}", page, size, resourceType);

        // TODO: 实现分页查询用户教学资源
        return Arrays.asList();
    }

    @Override
    public void deleteTeachingResource(Long resourceId) {
        log.info("删除教学资源，资源ID: {}", resourceId);

        TeachingResource resource = teachingResourceMapper.selectById(resourceId);
        if (resource == null) {
            throw new RuntimeException("教学资源不存在");
        }

        // 验证权限：只能删除自己创建的资源
        Long currentUserId = SecurityUtils.getCurrentUserId();
        if (!resource.getUserId().equals(currentUserId)) {
            throw new RuntimeException("无权限删除该资源");
        }

        teachingResourceMapper.deleteById(resourceId);
    }

    @Override
    public ComprehensionQuestionsResponse generateComprehensionQuestions(Long analysisId, Integer questionCount, String difficulty) {
        log.info("生成阅读理解题，分析ID: {}, 题目数量: {}", analysisId, questionCount);
        
        // TODO: 实现阅读理解题生成逻辑
        ComprehensionQuestionsResponse response = new ComprehensionQuestionsResponse();
        response.setResourceId(System.currentTimeMillis()); // 临时ID
        response.setDifficulty(difficulty);
        response.setTotalCount(questionCount);
        response.setQuestions(Arrays.asList());
        
        return response;
    }

    @Override
    public ReadingGuideResponse generateReadingGuide(Long analysisId, String studentLevel) {
        log.info("生成阅读指导，分析ID: {}, 学生水平: {}", analysisId, studentLevel);
        
        // TODO: 实现阅读指导生成逻辑
        ReadingGuideResponse response = new ReadingGuideResponse();
        response.setResourceId(System.currentTimeMillis()); // 临时ID
        response.setStudentLevel(studentLevel);
        
        ReadingGuide guide = new ReadingGuide();
        guide.setPreparationSteps(Arrays.asList("预习生词", "了解背景"));
        guide.setReadingStrategies(Arrays.asList("分段阅读", "关键词标记"));
        guide.setDiscussionPoints(Arrays.asList("主题讨论", "感受分享"));
        guide.setExtensionActivities(Arrays.asList("相关阅读", "创作练习"));
        guide.setTimeEstimate("30-45分钟");
        
        response.setGuide(guide);
        return response;
    }

    @Override
    public List<TeachingResourceResponse> getResourceHistory(Integer page, Integer size, String resourceType) {
        // TODO: 实现教学资源历史查询
        log.info("获取教学资源历史，页码: {}, 大小: {}, 类型: {}", page, size, resourceType);
        return Arrays.asList();
    }

    @Override
    public byte[] exportTeachingResource(Long resourceId, String format) {
        // TODO: 实现教学资源导出
        log.info("导出教学资源，资源ID: {}, 格式: {}", resourceId, format);
        return new byte[0];
    }

    @Override
    public void shareTeachingResource(Long resourceId, ShareResourceRequest request) {
        // TODO: 实现教学资源分享
        log.info("分享教学资源，资源ID: {}, 目标用户: {}", resourceId, request.getUserIds());
    }

    @Override
    public void rateTeachingResource(Long resourceId, RateResourceRequest request) {
        // TODO: 实现教学资源评价
        log.info("评价教学资源，资源ID: {}, 评分: {}", resourceId, request.getRating());
    }

    @Override
    public List<TeachingResourceResponse> getPopularResources(String resourceType, String gradeLevel, Integer count) {
        // TODO: 实现热门教学资源查询
        log.info("获取热门教学资源，类型: {}, 年级: {}, 数量: {}", resourceType, gradeLevel, count);
        return Arrays.asList();
    }

    @Override
    public List<TeachingResourceResponse> searchResources(String keyword, ResourceSearchFilters filters) {
        // TODO: 实现教学资源搜索
        log.info("搜索教学资源，关键词: {}", keyword);
        return Arrays.asList();
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        // TODO: 从安全上下文获取当前用户ID
        return 1L; // 临时返回
    }

    /**
     * 将AI资源转换为JSON
     */
    private String convertToJson(AIAnalysisService.TeachingResources resources) {
        // TODO: 实现对象到JSON的转换
        return "{}";
    }

    /**
     * 将AI生成的资源转换为响应格式
     */
    private void convertAIResourcesToResponse(AIAnalysisService.TeachingResources aiResources, TeachingResourceResponse response) {
        // TODO: 实现AI资源到响应的转换
    }

    /**
     * 将AI推荐文本转换为响应格式
     */
    private TextRecommendationResponse convertToRecommendationResponse(AIAnalysisService.RecommendedText aiText) {
        TextRecommendationResponse response = new TextRecommendationResponse();
        response.setTextId(aiText.getId());
        response.setTitle(aiText.getTitle());
        response.setContent(aiText.getContent());
        response.setAuthor("示例作者");
        response.setSource("示例来源");
        
        // 构建分级信息
        TextRecommendationResponse.GradeInfo gradeInfo = new TextRecommendationResponse.GradeInfo();
        gradeInfo.setGradeCode(aiText.getGradeLevel());
        gradeInfo.setDifficultyScore(aiText.getDifficulty());
        response.setGradeInfo(gradeInfo);
        
        // 构建推荐信息
        TextRecommendationResponse.RecommendationInfo recommendInfo = new TextRecommendationResponse.RecommendationInfo();
        recommendInfo.setMatchScore(aiText.getMatchScore());
        recommendInfo.setRecommendReason(aiText.getRecommendReason());
        response.setRecommendationInfo(recommendInfo);
        
        // 构建统计信息
        TextRecommendationResponse.TextStats stats = new TextRecommendationResponse.TextStats();
        stats.setWordCount(aiText.getLength());
        stats.setReadingTime(aiText.getLength() / 200); // 假设每分钟200字
        response.setStats(stats);
        
        return response;
    }

    /**
     * 将教学资源实体转换为响应DTO
     */
    private TeachingResourceResponse convertToResourceResponse(TeachingResource resource) {
        TeachingResourceResponse response = new TeachingResourceResponse();
        response.setResourceId(resource.getId());
        response.setTitle(resource.getTitle());
        response.setResourceType(resource.getResourceType());
        response.setTargetGrade(resource.getTargetGrade());
        response.setStudentLevel(resource.getStudentLevel());
        response.setDescription(resource.getDescription());
        response.setDifficultyLevel(resource.getDifficultyLevel());
        response.setEstimatedTime(resource.getEstimatedTime());
        response.setUsageCount(resource.getUsageCount());
        response.setAverageRating(resource.getAverageRating());
        response.setGenerateTime(resource.getCreateTime());

        // TODO: 解析JSON内容并设置具体的资源内容

        return response;
    }
}
