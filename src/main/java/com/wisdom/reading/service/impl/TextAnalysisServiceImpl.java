package com.wisdom.reading.service.impl;

import com.wisdom.reading.controller.TextAnalysisController.GradeStandard;
import com.wisdom.reading.dto.TextAnalysisRequest;
import com.wisdom.reading.dto.TextAnalysisResponse;
import com.wisdom.reading.entity.TextAnalysis;
import com.wisdom.reading.mapper.TextAnalysisMapper;
import com.wisdom.reading.service.TextAnalysisService;
import com.wisdom.reading.service.AIAnalysisService;
import com.wisdom.reading.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文本分析服务实现类
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TextAnalysisServiceImpl implements TextAnalysisService {

    private final TextAnalysisMapper textAnalysisMapper;
    private final AIAnalysisService aiAnalysisService;

    @Override
    @Transactional
    public TextAnalysisResponse analyzeText(TextAnalysisRequest request) {
        log.info("开始分析文本，长度: {}", request.getText().length());
        
        try {
            // 1. 调用AI服务进行文本分析
            TextAnalysisResponse aiResponse = aiAnalysisService.analyzeText(request);
            
            // 2. 保存分析结果到数据库
            TextAnalysis analysis = new TextAnalysis();
            analysis.setUserId(SecurityUtils.getCurrentUserId());
            analysis.setOriginalText(request.getText());
            analysis.setTitle(request.getTitle());
            analysis.setSource(request.getSource());
            analysis.setGradeLevel(aiResponse.getGradeResult().getGradeCode());
            analysis.setConfidence(aiResponse.getGradeResult().getConfidence());
            analysis.setTextLength(aiResponse.getBasicStats().getTextLength());
            analysis.setWordCount(aiResponse.getBasicStats().getWordCount());
            analysis.setSentenceCount(aiResponse.getBasicStats().getSentenceCount());
            analysis.setParagraphCount(aiResponse.getBasicStats().getParagraphCount());
            analysis.setNewWordRate(aiResponse.getVocabularyAnalysis().getNewWordRate());
            analysis.setAvgSentenceLength(aiResponse.getBasicStats().getAvgSentenceLength());
            analysis.setComplexSentenceRate(aiResponse.getSentenceAnalysis().getComplexSentenceRate());
            analysis.setTopicCategory(aiResponse.getTopicAnalysis().getCategory());
            analysis.setStatus("SUCCESS");
            analysis.setCreateTime(LocalDateTime.now());
            
            textAnalysisMapper.insert(analysis);
            aiResponse.setAnalysisId(analysis.getId());
            
            log.info("文本分析完成，分析ID: {}", analysis.getId());
            return aiResponse;
            
        } catch (Exception e) {
            log.error("文本分析失败", e);
            
            // 保存失败记录
            TextAnalysis analysis = new TextAnalysis();
            analysis.setOriginalText(request.getText());
            analysis.setTitle(request.getTitle());
            analysis.setSource(request.getSource());
            analysis.setStatus("FAILED");
            analysis.setErrorMessage(e.getMessage());
            analysis.setCreateTime(LocalDateTime.now());
            textAnalysisMapper.insert(analysis);
            
            throw new RuntimeException("文本分析失败: " + e.getMessage());
        }
    }

    @Override
    public TextAnalysisResponse analyzeFile(MultipartFile file, String title, Boolean detailedAnalysis) {
        log.info("开始分析文件: {}", file.getOriginalFilename());
        
        try {
            // 1. 提取文件内容
            String text = extractTextFromFile(file);
            
            // 2. 构建分析请求
            TextAnalysisRequest request = new TextAnalysisRequest();
            request.setText(text);
            request.setTitle(title != null ? title : file.getOriginalFilename());
            request.setSource("文件上传: " + file.getOriginalFilename());
            request.setDetailedAnalysis(detailedAnalysis);
            
            // 3. 调用文本分析
            return analyzeText(request);
            
        } catch (Exception e) {
            log.error("文件分析失败", e);
            throw new RuntimeException("文件分析失败: " + e.getMessage());
        }
    }

    @Override
    public List<TextAnalysisResponse> batchAnalyze(List<TextAnalysisRequest> requests) {
        log.info("开始批量分析，数量: {}", requests.size());
        
        return requests.stream()
                .map(this::analyzeText)
                .collect(Collectors.toList());
    }

    @Override
    public List<TextAnalysisResponse> getAnalysisHistory(Integer page, Integer size, String gradeLevel) {
        // TODO: 实现分页查询分析历史
        log.info("获取分析历史，页码: {}, 大小: {}, 分级: {}", page, size, gradeLevel);
        
        // 这里返回模拟数据，实际应该从数据库查询
        return Arrays.asList();
    }

    @Override
    public TextAnalysisResponse getAnalysisDetail(Long analysisId) {
        log.info("获取分析详情，ID: {}", analysisId);
        
        TextAnalysis analysis = textAnalysisMapper.selectById(analysisId);
        if (analysis == null) {
            throw new RuntimeException("分析记录不存在");
        }
        
        // TODO: 将数据库记录转换为响应DTO
        return convertToResponse(analysis);
    }

    @Override
    public void deleteAnalysis(Long analysisId) {
        log.info("删除分析记录，ID: {}", analysisId);
        textAnalysisMapper.deleteById(analysisId);
    }

    @Override
    public List<GradeStandard> getGradeStandards() {
        log.info("获取分级标准");

        // 返回系统支持的分级标准
        return Arrays.asList(
            new GradeStandard("PRIMARY_LOW", "小学低年级", "1-2年级", "#67C23A", 6, 8),
            new GradeStandard("PRIMARY_MID", "小学中年级", "3-4年级", "#E6A23C", 8, 10),
            new GradeStandard("PRIMARY_HIGH", "小学高年级", "5-6年级", "#F56C6C", 10, 12),
            new GradeStandard("JUNIOR_HIGH", "初中阶段", "7-9年级", "#909399", 12, 15),
            new GradeStandard("SENIOR_HIGH", "高中阶段", "10-12年级", "#606266", 15, 18)
        );
    }

    /**
     * 从文件中提取文本内容
     */
    private String extractTextFromFile(MultipartFile file) {
        // TODO: 根据文件类型提取文本内容
        // 支持 .txt, .doc, .docx, .pdf 等格式
        try {
            return new String(file.getBytes(), "UTF-8");
        } catch (Exception e) {
            throw new RuntimeException("文件内容提取失败: " + e.getMessage());
        }
    }

    /**
     * 将数据库实体转换为响应DTO
     */
    private TextAnalysisResponse convertToResponse(TextAnalysis analysis) {
        // TODO: 实现实体到DTO的转换
        TextAnalysisResponse response = new TextAnalysisResponse();
        response.setAnalysisId(analysis.getId());
        response.setTitle(analysis.getTitle());
        response.setAnalysisTime(analysis.getCreateTime());
        
        // 构建分级结果
        TextAnalysisResponse.GradeResult gradeResult = new TextAnalysisResponse.GradeResult();
        gradeResult.setGradeCode(analysis.getGradeLevel());
        gradeResult.setConfidence(analysis.getConfidence());
        response.setGradeResult(gradeResult);
        
        // 构建基础统计
        TextAnalysisResponse.BasicStatistics basicStats = new TextAnalysisResponse.BasicStatistics();
        basicStats.setTextLength(analysis.getTextLength());
        basicStats.setWordCount(analysis.getWordCount());
        basicStats.setSentenceCount(analysis.getSentenceCount());
        basicStats.setParagraphCount(analysis.getParagraphCount());
        basicStats.setAvgSentenceLength(analysis.getAvgSentenceLength());
        response.setBasicStats(basicStats);
        
        return response;
    }
}
