package com.wisdom.reading.service.impl;

import com.wisdom.reading.dto.*;
import com.wisdom.reading.entity.TextAnalysis;
import com.wisdom.reading.entity.TextSimplification;
import com.wisdom.reading.mapper.TextAnalysisMapper;
import com.wisdom.reading.mapper.TextSimplificationMapper;
import com.wisdom.reading.service.AIAnalysisService;
import com.wisdom.reading.service.TextSimplificationService;
import com.wisdom.reading.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文本简化服务实现类
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TextSimplificationServiceImpl implements TextSimplificationService {

    private final TextSimplificationMapper textSimplificationMapper;
    private final TextAnalysisMapper textAnalysisMapper;
    private final AIAnalysisService aiAnalysisService;

    @Override
    @Transactional
    public TextSimplificationResponse simplifyText(TextSimplificationRequest request) {
        log.info("开始简化文本，目标年级: {}, 简化程度: {}", request.getTargetGrade(), request.getSimplificationLevel());
        
        try {
            // 1. 调用AI服务进行文本简化
            TextSimplificationResponse aiResponse = aiAnalysisService.simplifyText(request);
            
            // 2. 保存简化记录到数据库
            TextSimplification simplification = new TextSimplification();
            simplification.setUserId(SecurityUtils.getCurrentUserId());
            simplification.setAnalysisId(request.getAnalysisId());
            simplification.setOriginalText(request.getText());
            simplification.setSimplifiedText(aiResponse.getSimplifiedText());
            simplification.setTargetGrade(request.getTargetGrade());
            simplification.setSimplificationLevel(request.getSimplificationLevel());
            simplification.setOriginalGrade(aiResponse.getOriginalGrade().getGradeCode());
            simplification.setSimplifiedGrade(aiResponse.getSimplifiedGrade().getGradeCode());
            simplification.setSimplificationRatio(aiResponse.getStatistics().getSimplificationRatio());
            simplification.setQualityScore(aiResponse.getQualityAssessment().getOverallScore());
            simplification.setStatus("SUCCESS");
            simplification.setCreateTime(LocalDateTime.now());
            
            textSimplificationMapper.insert(simplification);
            aiResponse.setSimplificationId(simplification.getId());
            
            log.info("文本简化完成，简化ID: {}", simplification.getId());
            return aiResponse;
            
        } catch (Exception e) {
            log.error("文本简化失败", e);
            throw new RuntimeException("文本简化失败: " + e.getMessage());
        }
    }

    @Override
    public TextSimplificationResponse simplifyFromAnalysis(Long analysisId, String targetGrade, String simplificationLevel) {
        log.info("基于分析结果简化文本，分析ID: {}, 目标年级: {}", analysisId, targetGrade);
        
        TextAnalysis analysis = textAnalysisMapper.selectById(analysisId);
        if (analysis == null) {
            throw new RuntimeException("文本分析记录不存在");
        }
        
        TextSimplificationRequest request = new TextSimplificationRequest();
        request.setText(analysis.getOriginalText());
        request.setTargetGrade(targetGrade);
        request.setAnalysisId(analysisId);
        request.setSimplificationLevel(simplificationLevel);
        
        return simplifyText(request);
    }

    @Override
    public List<TextSimplificationResponse> batchSimplify(List<TextSimplificationRequest> requests) {
        log.info("开始批量简化，数量: {}", requests.size());
        
        return requests.stream()
                .map(this::simplifyText)
                .collect(Collectors.toList());
    }

    @Override
    public List<TextSimplificationResponse> getSimplificationHistory(Integer page, Integer size, String targetGrade) {
        // TODO: 实现分页查询简化历史
        log.info("获取简化历史，页码: {}, 大小: {}, 目标年级: {}", page, size, targetGrade);
        
        // 这里返回模拟数据，实际应该从数据库查询
        return Arrays.asList();
    }

    @Override
    public TextSimplificationResponse getSimplificationDetail(Long simplificationId) {
        log.info("获取简化详情，ID: {}", simplificationId);
        
        TextSimplification simplification = textSimplificationMapper.selectById(simplificationId);
        if (simplification == null) {
            throw new RuntimeException("简化记录不存在");
        }
        
        // TODO: 将数据库记录转换为响应DTO
        return convertToResponse(simplification);
    }

    @Override
    public void deleteSimplification(Long simplificationId) {
        log.info("删除简化记录，ID: {}", simplificationId);
        textSimplificationMapper.deleteById(simplificationId);
    }

    @Override
    public TextSimplificationResponse.ComparisonResult compareSimplification(Long simplificationId) {
        log.info("比较简化效果，ID: {}", simplificationId);
        
        // TODO: 实现简化效果比较逻辑
        return new TextSimplificationResponse.ComparisonResult();
    }

    @Override
    public TextSimplificationResponse resimplify(Long simplificationId, String newTargetGrade, String newSimplificationLevel) {
        log.info("重新简化，原ID: {}, 新目标年级: {}", simplificationId, newTargetGrade);
        
        TextSimplification originalSimplification = textSimplificationMapper.selectById(simplificationId);
        if (originalSimplification == null) {
            throw new RuntimeException("原简化记录不存在");
        }
        
        TextSimplificationRequest request = new TextSimplificationRequest();
        request.setText(originalSimplification.getOriginalText());
        request.setTargetGrade(newTargetGrade);
        request.setAnalysisId(originalSimplification.getAnalysisId());
        request.setSimplificationLevel(newSimplificationLevel);
        
        return simplifyText(request);
    }

    @Override
    public QualityAssessment evaluateSimplification(Long simplificationId) {
        log.info("评估简化效果，简化ID: {}", simplificationId);

        TextSimplification simplification = textSimplificationMapper.selectById(simplificationId);
        if (simplification == null) {
            throw new RuntimeException("简化记录不存在");
        }

        // TODO: 实现简化效果评估逻辑
        QualityAssessment assessment = new QualityAssessment();
        assessment.setOverallScore(simplification.getQualityScore());
        assessment.setReadabilityScore(85.0);
        assessment.setContentRetentionScore(80.0);
        assessment.setFluencyScore(90.0);
        assessment.setAccuracyScore(88.0);
        assessment.setSuitabilityScore(87.0);
        assessment.setQualityLevel("GOOD");
        assessment.setRecommended(true);
        assessment.setAssessmentNotes("简化效果良好，保持了原文主要内容");

        return assessment;
    }

    /**
     * 将数据库记录转换为响应DTO
     */
    private TextSimplificationResponse convertToResponse(TextSimplification simplification) {
        // TODO: 实现转换逻辑
        TextSimplificationResponse response = new TextSimplificationResponse();
        response.setSimplificationId(simplification.getId());
        response.setOriginalText(simplification.getOriginalText());
        response.setSimplifiedText(simplification.getSimplifiedText());
        response.setTargetGrade(simplification.getTargetGrade());
        response.setSimplificationTime(simplification.getCreateTime());
        
        return response;
    }
}
