package com.wisdom.reading.service;

import com.wisdom.reading.controller.TextAnalysisController.GradeStandard;
import com.wisdom.reading.controller.TextAnalysisController.GradeStandard;
import com.wisdom.reading.dto.TextAnalysisRequest;
import com.wisdom.reading.dto.TextAnalysisResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文本分析服务接口
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
public interface TextAnalysisService {

    /**
     * 分析文本
     * 
     * @param request 分析请求
     * @return 分析结果
     */
    TextAnalysisResponse analyzeText(TextAnalysisRequest request);

    /**
     * 分析文件
     * 
     * @param file 文本文件
     * @param title 文件标题
     * @param detailedAnalysis 是否详细分析
     * @return 分析结果
     */
    TextAnalysisResponse analyzeFile(MultipartFile file, String title, Boolean detailedAnalysis);

    /**
     * 批量分析文本
     * 
     * @param requests 分析请求列表
     * @return 分析结果列表
     */
    List<TextAnalysisResponse> batchAnalyze(List<TextAnalysisRequest> requests);

    /**
     * 获取分析历史
     * 
     * @param page 页码
     * @param size 每页大小
     * @param gradeLevel 分级筛选
     * @return 分析历史列表
     */
    List<TextAnalysisResponse> getAnalysisHistory(Integer page, Integer size, String gradeLevel);

    /**
     * 获取分析详情
     * 
     * @param analysisId 分析ID
     * @return 分析详情
     */
    TextAnalysisResponse getAnalysisDetail(Long analysisId);

    /**
     * 删除分析记录
     * 
     * @param analysisId 分析ID
     */
    void deleteAnalysis(Long analysisId);

    /**
     * 获取分级标准
     * 
     * @return 分级标准列表
     */
    List<GradeStandard> getGradeStandards();
}
