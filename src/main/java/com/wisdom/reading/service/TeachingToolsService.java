package com.wisdom.reading.service;

import com.wisdom.reading.controller.TeachingToolsController.*;
import com.wisdom.reading.dto.TeachingResourceRequest;
import com.wisdom.reading.dto.TeachingResourceResponse;
import com.wisdom.reading.dto.TextRecommendationRequest;
import com.wisdom.reading.dto.TextRecommendationResponse;

import java.util.List;

/**
 * 教学工具服务接口
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
public interface TeachingToolsService {

    /**
     * 生成教学资源
     * 
     * @param request 教学资源请求
     * @return 教学资源响应
     */
    TeachingResourceResponse generateTeachingResources(TeachingResourceRequest request);

    /**
     * 推荐文本
     * 
     * @param request 推荐请求
     * @return 推荐文本列表
     */
    List<TextRecommendationResponse> recommendTexts(TextRecommendationRequest request);

    /**
     * 获取同级文本
     * 
     * @param analysisId 分析ID
     * @param count 推荐数量
     * @return 同级文本列表
     */
    List<TextRecommendationResponse> getSimilarTexts(Long analysisId, Integer count);

    /**
     * 生成生词卡片
     *
     * @param analysisId 分析ID
     * @param targetGrade 目标年级
     * @return 生词卡片响应
     */
    VocabularyCardsResponse generateVocabularyCards(Long analysisId, String targetGrade);

    /**
     * 评价教学资源
     *
     * @param resourceId 资源ID
     * @param request 评价请求
     */
    void rateTeachingResource(Long resourceId, RateResourceRequest request);

    /**
     * 获取教学资源详情
     *
     * @param resourceId 资源ID
     * @return 教学资源响应
     */
    TeachingResourceResponse getTeachingResourceDetail(Long resourceId);

    /**
     * 获取用户的教学资源列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param resourceType 资源类型筛选
     * @return 教学资源列表
     */
    List<TeachingResourceResponse> getUserTeachingResources(Integer page, Integer size, String resourceType);

    /**
     * 删除教学资源
     *
     * @param resourceId 资源ID
     */
    void deleteTeachingResource(Long resourceId);

    /**
     * 生成阅读理解题
     * 
     * @param analysisId 分析ID
     * @param questionCount 题目数量
     * @param difficulty 难度等级
     * @return 阅读理解题响应
     */
    ComprehensionQuestionsResponse generateComprehensionQuestions(Long analysisId, Integer questionCount, String difficulty);

    /**
     * 生成阅读指导
     * 
     * @param analysisId 分析ID
     * @param studentLevel 学生水平
     * @return 阅读指导响应
     */
    ReadingGuideResponse generateReadingGuide(Long analysisId, String studentLevel);

    /**
     * 获取教学资源历史
     * 
     * @param page 页码
     * @param size 每页大小
     * @param resourceType 资源类型
     * @return 教学资源列表
     */
    List<TeachingResourceResponse> getResourceHistory(Integer page, Integer size, String resourceType);

    /**
     * 导出教学资源
     * 
     * @param resourceId 资源ID
     * @param format 导出格式
     * @return 文件数据
     */
    byte[] exportTeachingResource(Long resourceId, String format);

    /**
     * 分享教学资源
     * 
     * @param resourceId 资源ID
     * @param request 分享请求
     */
    void shareTeachingResource(Long resourceId, ShareResourceRequest request);

    /**
     * 评价教学资源
     * 
     * @param resourceId 资源ID
     * @param request 评价请求
     */
    void rateTeachingResource(Long resourceId, RateResourceRequest request);

    /**
     * 获取热门教学资源
     * 
     * @param resourceType 资源类型
     * @param gradeLevel 年级水平
     * @param count 数量
     * @return 热门资源列表
     */
    List<TeachingResourceResponse> getPopularResources(String resourceType, String gradeLevel, Integer count);

    /**
     * 搜索教学资源
     * 
     * @param keyword 关键词
     * @param filters 过滤条件
     * @return 搜索结果
     */
    List<TeachingResourceResponse> searchResources(String keyword, ResourceSearchFilters filters);

    /**
     * 资源搜索过滤条件
     */
    class ResourceSearchFilters {
        private String resourceType;
        private String gradeLevel;
        private String difficulty;
        private String author;
        private Double minRating;
        
        // 构造函数、getter和setter省略
    }
}
