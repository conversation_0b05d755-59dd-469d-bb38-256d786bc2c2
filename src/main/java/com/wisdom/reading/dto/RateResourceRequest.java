package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;

/**
 * 资源评价请求DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RateResourceRequest {

    /**
     * 评分 (1-5)
     */
    @Min(value = 1, message = "评分不能小于1")
    @Max(value = 5, message = "评分不能大于5")
    private Integer rating;

    /**
     * 评价内容
     */
    @Size(max = 500, message = "评价内容不能超过500字符")
    private String comment;

    /**
     * 有用性评分 (1-5)
     */
    @Min(value = 1, message = "有用性评分不能小于1")
    @Max(value = 5, message = "有用性评分不能大于5")
    private Integer usefulnessRating;

    /**
     * 准确性评分 (1-5)
     */
    @Min(value = 1, message = "准确性评分不能小于1")
    @Max(value = 5, message = "准确性评分不能大于5")
    private Integer accuracyRating;

    /**
     * 易用性评分 (1-5)
     */
    @Min(value = 1, message = "易用性评分不能小于1")
    @Max(value = 5, message = "易用性评分不能大于5")
    private Integer usabilityRating;

    /**
     * 是否推荐
     */
    private Boolean recommended;

    /**
     * 推荐理由
     */
    @Size(max = 200, message = "推荐理由不能超过200字符")
    private String recommendationReason;

    /**
     * 改进建议
     */
    @Size(max = 300, message = "改进建议不能超过300字符")
    private String improvementSuggestion;

    /**
     * 使用场景
     */
    private String useCase;

    /**
     * 用户角色：STUDENT(学生), PARENT(家长), TEACHER(教师)
     */
    private String userRole;
}
