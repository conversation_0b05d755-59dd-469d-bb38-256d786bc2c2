package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 阅读理解题响应DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ComprehensionQuestionsResponse {

    /**
     * 资源ID
     */
    private Long resourceId;

    /**
     * 关联的分析ID
     */
    private Long analysisId;

    /**
     * 题目列表
     */
    private List<Question> questions;

    /**
     * 总题目数量
     */
    private Integer totalCount;

    /**
     * 总分值
     */
    private Integer totalPoints;

    /**
     * 预计完成时间（分钟）
     */
    private Integer estimatedTime;

    /**
     * 适用年级
     */
    private String targetGrade;

    /**
     * 难度等级
     */
    private String difficultyLevel;

    /**
     * 生成时间
     */
    private LocalDateTime generateTime;

    /**
     * 使用说明
     */
    private String instructions;

    /**
     * 题目类型分布
     */
    private QuestionTypeDistribution typeDistribution;

    /**
     * 题目信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Question {
        /**
         * 题目ID
         */
        private String questionId;

        /**
         * 题目类型：CHOICE(选择题), FILL_BLANK(填空题), SHORT_ANSWER(简答题), ESSAY(作文题)
         */
        private String type;

        /**
         * 题目内容
         */
        private String question;

        /**
         * 选项（选择题用）
         */
        private List<String> options;

        /**
         * 正确答案
         */
        private String correctAnswer;

        /**
         * 答案解析
         */
        private String explanation;

        /**
         * 分值
         */
        private Integer points;

        /**
         * 难度等级：EASY(简单), MEDIUM(中等), HARD(困难)
         */
        private String difficulty;

        /**
         * 考查能力
         */
        private List<String> skills;

        /**
         * 相关文本段落
         */
        private String relatedParagraph;

        /**
         * 提示信息
         */
        private String hint;
    }

    /**
     * 题目类型分布
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class QuestionTypeDistribution {
        /**
         * 选择题数量
         */
        private Integer choiceCount;

        /**
         * 填空题数量
         */
        private Integer fillBlankCount;

        /**
         * 简答题数量
         */
        private Integer shortAnswerCount;

        /**
         * 作文题数量
         */
        private Integer essayCount;
    }
}
