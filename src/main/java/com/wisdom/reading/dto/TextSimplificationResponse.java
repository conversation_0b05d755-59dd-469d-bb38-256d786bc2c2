package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文本简化响应DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
public class TextSimplificationResponse {

    /**
     * 简化记录ID
     */
    private Long simplificationId;

    /**
     * 原始文本
     */
    private String originalText;

    /**
     * 简化后文本
     */
    private String simplifiedText;

    /**
     * 目标年级
     */
    private String targetGrade;

    /**
     * 简化前分级
     */
    private GradeInfo originalGrade;

    /**
     * 简化后分级
     */
    private GradeInfo simplifiedGrade;

    /**
     * 简化统计
     */
    private SimplificationStatistics statistics;

    /**
     * 词汇替换列表
     */
    private List<WordReplacement> wordReplacements;

    /**
     * 句式调整列表
     */
    private List<SentenceAdjustment> sentenceAdjustments;

    /**
     * 简化质量评估
     */
    private QualityAssessment qualityAssessment;

    /**
     * 简化时间
     */
    private LocalDateTime simplificationTime;

    /**
     * 比较结果
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ComparisonResult {
        /**
         * 原文分析结果
         */
        private TextAnalysisResponse originalAnalysis;

        /**
         * 简化文分析结果
         */
        private TextAnalysisResponse simplifiedAnalysis;

        /**
         * 改进指标
         */
        private ImprovementMetrics improvements;

        /**
         * 对比说明
         */
        private String comparisonNotes;
    }

    /**
     * 改进指标
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ImprovementMetrics {
        /**
         * 可读性改进度
         */
        private Double readabilityImprovement;

        /**
         * 词汇难度降低度
         */
        private Double vocabularySimplification;

        /**
         * 句式复杂度降低度
         */
        private Double syntaxSimplification;

        /**
         * 整体改进评分
         */
        private Double overallImprovement;
    }
}
