package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文本简化响应DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
public class TextSimplificationResponse {

    /**
     * 简化记录ID
     */
    private Long simplificationId;

    /**
     * 原始文本
     */
    private String originalText;

    /**
     * 简化后文本
     */
    private String simplifiedText;

    /**
     * 目标年级
     */
    private String targetGrade;

    /**
     * 简化前分级
     */
    private GradeInfo originalGrade;

    /**
     * 简化后分级
     */
    private GradeInfo simplifiedGrade;

    /**
     * 简化统计
     */
    private SimplificationStatistics statistics;

    /**
     * 词汇替换列表
     */
    private List<WordReplacement> wordReplacements;

    /**
     * 句式调整列表
     */
    private List<SentenceAdjustment> sentenceAdjustments;

    /**
     * 简化质量评估
     */
    private QualityAssessment qualityAssessment;

    /**
     * 简化时间
     */
    private LocalDateTime simplificationTime;

    /**
     * 比较结果
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ComparisonResult {
        /**
         * 原文分析结果
         */
        private TextAnalysisResponse originalAnalysis;

        /**
         * 简化文分析结果
         */
        private TextAnalysisResponse simplifiedAnalysis;

        /**
         * 改进指标
         */
        private ImprovementMetrics improvements;

        /**
         * 对比说明
         */
        private String comparisonNotes;
    }

    /**
     * 改进指标
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ImprovementMetrics {
        /**
         * 可读性改进度
         */
        private Double readabilityImprovement;

        /**
         * 词汇难度降低度
         */
        private Double vocabularySimplification;

        /**
         * 句式复杂度降低度
         */
        private Double syntaxSimplification;

        /**
         * 整体改进评分
         */
        private Double overallImprovement;
    }

    /**
     * 分级信息
     */
    @Data
    public static class GradeInfo {
        /**
         * 分级代码
         */
        private String gradeCode;

        /**
         * 分级名称
         */
        private String gradeName;

        /**
         * 置信度
         */
        private Double confidence;
    }

    /**
     * 简化统计
     */
    @Data
    public static class SimplificationStatistics {
        /**
         * 原文字数
         */
        private Integer originalWordCount;

        /**
         * 简化后字数
         */
        private Integer simplifiedWordCount;

        /**
         * 字数变化率
         */
        private Double wordCountChangeRate;

        /**
         * 原文句数
         */
        private Integer originalSentenceCount;

        /**
         * 简化后句数
         */
        private Integer simplifiedSentenceCount;

        /**
         * 句数变化率
         */
        private Double sentenceCountChangeRate;

        /**
         * 简化比例
         */
        private Double simplificationRatio;
    }

    /**
     * 词汇替换
     */
    @Data
    public static class WordReplacement {
        /**
         * 原词汇
         */
        private String originalWord;

        /**
         * 替换词汇
         */
        private String replacementWord;

        /**
         * 替换原因
         */
        private String reason;

        /**
         * 出现位置
         */
        private List<Integer> positions;

        /**
         * 难度降低程度
         */
        private String difficultyReduction;
    }

    /**
     * 句式调整
     */
    @Data
    public static class SentenceAdjustment {
        /**
         * 原句子
         */
        private String originalSentence;

        /**
         * 调整后句子
         */
        private String adjustedSentence;

        /**
         * 调整类型：SPLIT(拆分), SIMPLIFY(简化), REORDER(重组)
         */
        private String adjustmentType;

        /**
         * 调整说明
         */
        private String explanation;

        /**
         * 句子位置
         */
        private Integer position;
    }

    /**
     * 质量评估
     */
    @Data
    public static class QualityAssessment {
        /**
         * 整体质量评分 (0-100)
         */
        private Double overallScore;

        /**
         * 可读性改善度 (0-100)
         */
        private Double readabilityImprovement;

        /**
         * 内容保真度 (0-100)
         */
        private Double contentFidelity;

        /**
         * 语言流畅度 (0-100)
         */
        private Double languageFluency;

        /**
         * 评估说明
         */
        private String assessmentNotes;

        /**
         * 改进建议
         */
        private List<String> improvementSuggestions;
    }
}
