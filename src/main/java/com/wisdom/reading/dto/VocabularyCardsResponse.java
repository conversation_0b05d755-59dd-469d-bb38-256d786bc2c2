package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 生词卡片响应DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VocabularyCardsResponse {

    /**
     * 资源ID
     */
    private Long resourceId;

    /**
     * 生词卡片列表
     */
    private List<VocabularyCard> cards;

    /**
     * 目标年级
     */
    private String targetGrade;

    /**
     * 卡片总数
     */
    private Integer totalCount;

    /**
     * 重点词汇数量
     */
    private Integer keyWordCount;

    /**
     * 难度分布
     */
    private DifficultyDistribution difficultyDistribution;

    /**
     * 词性分布
     */
    private List<PartOfSpeechCount> partOfSpeechDistribution;

    /**
     * 生成时间
     */
    private LocalDateTime generateTime;

    /**
     * 基于的分析ID
     */
    private Long baseAnalysisId;

    /**
     * 学习建议
     */
    private String learningAdvice;

    /**
     * 预计学习时间（分钟）
     */
    private Integer estimatedLearningTime;

    /**
     * 难度分布
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DifficultyDistribution {
        private Integer easyCount;
        private Integer mediumCount;
        private Integer hardCount;
    }

    /**
     * 词性统计
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PartOfSpeechCount {
        private String partOfSpeech;
        private Integer count;
        private Double percentage;
    }
}
