package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 词汇替换信息DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WordReplacement {

    /**
     * 原始词汇
     */
    private String originalWord;

    /**
     * 替换词汇
     */
    private String replacementWord;

    /**
     * 词性
     */
    private String partOfSpeech;

    /**
     * 原始词汇难度等级
     */
    private Integer originalDifficulty;

    /**
     * 替换词汇难度等级
     */
    private Integer replacementDifficulty;

    /**
     * 替换原因
     */
    private String replacementReason;

    /**
     * 在文本中的位置
     */
    private Integer position;

    /**
     * 上下文
     */
    private String context;

    /**
     * 替换类型：SYNONYM(同义词), SIMPLER(更简单), COMMON(更常用)
     */
    private String replacementType;

    /**
     * 置信度
     */
    private Double confidence;

    /**
     * 是否必要替换
     */
    private Boolean necessary;

    /**
     * 替换建议
     */
    private String suggestion;
}
