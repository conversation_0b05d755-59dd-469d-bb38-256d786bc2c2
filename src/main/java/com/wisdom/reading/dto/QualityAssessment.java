package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 质量评估信息DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QualityAssessment {

    /**
     * 总体质量评分 (0-100)
     */
    private Double overallScore;

    /**
     * 可读性评分 (0-100)
     */
    private Double readabilityScore;

    /**
     * 内容保持度评分 (0-100)
     */
    private Double contentRetentionScore;

    /**
     * 语言流畅度评分 (0-100)
     */
    private Double fluencyScore;

    /**
     * 准确性评分 (0-100)
     */
    private Double accuracyScore;

    /**
     * 适合度评分 (0-100)
     */
    private Double suitabilityScore;

    /**
     * 质量等级：EXCELLENT(优秀), GOOD(良好), FAIR(一般), POOR(较差)
     */
    private String qualityLevel;

    /**
     * 评估说明
     */
    private String assessmentNotes;

    /**
     * 改进建议
     */
    private List<String> improvementSuggestions;

    /**
     * 优点列表
     */
    private List<String> strengths;

    /**
     * 缺点列表
     */
    private List<String> weaknesses;

    /**
     * 是否推荐使用
     */
    private Boolean recommended;

    /**
     * 推荐理由
     */
    private String recommendationReason;

    /**
     * 评估时间戳
     */
    private Long assessmentTimestamp;

    /**
     * 评估版本
     */
    private String assessmentVersion;
}
