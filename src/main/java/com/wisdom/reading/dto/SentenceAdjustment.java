package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 句式调整信息DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SentenceAdjustment {

    /**
     * 原始句子
     */
    private String originalSentence;

    /**
     * 调整后句子
     */
    private String adjustedSentence;

    /**
     * 调整类型：SPLIT(拆分), MERGE(合并), REORDER(重组), SIMPLIFY(简化)
     */
    private String adjustmentType;

    /**
     * 调整原因
     */
    private String adjustmentReason;

    /**
     * 原始句子长度
     */
    private Integer originalLength;

    /**
     * 调整后句子长度
     */
    private Integer adjustedLength;

    /**
     * 长度变化率
     */
    private Double lengthChangeRate;

    /**
     * 原始复杂度
     */
    private Double originalComplexity;

    /**
     * 调整后复杂度
     */
    private Double adjustedComplexity;

    /**
     * 复杂度改善度
     */
    private Double complexityImprovement;

    /**
     * 在文本中的位置
     */
    private Integer position;

    /**
     * 段落编号
     */
    private Integer paragraphNumber;

    /**
     * 置信度
     */
    private Double confidence;

    /**
     * 是否必要调整
     */
    private Boolean necessary;

    /**
     * 调整建议
     */
    private String suggestion;
}
