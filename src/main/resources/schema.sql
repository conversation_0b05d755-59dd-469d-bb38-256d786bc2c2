-- 智慧阅读系统数据库表结构

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `username` VARCHAR(50) NOT NULL UNIQUE,
    `password` VARCHAR(255) NOT NULL,
    `email` VARCHAR(100) UNIQUE,
    `phone` VARCHAR(20),
    `real_name` VARCHAR(50),
    `role` VARCHAR(20) NOT NULL DEFAULT 'STUDENT',
    `grade_level` VARCHAR(20),
    `school` VARCHAR(100),
    `avatar_url` VARCHAR(255),
    `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    `email_verified` <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE,
    `phone_verified` BOOLEAN DEFAULT FALSE,
    `last_login_time` DATETIME,
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted` <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE,
    INDEX `idx_username` (`username`),
    INDEX `idx_email` (`email`),
    INDEX `idx_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 文本分析表
CREATE TABLE IF NOT EXISTS `text_analysis` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT NOT NULL,
    `text_title` VARCHAR(200),
    `text_content` TEXT NOT NULL,
    `text_source` VARCHAR(100),
    `grade_level` VARCHAR(20),
    `topic_category` VARCHAR(50),
    `difficulty_score` DOUBLE,
    `readability_score` DOUBLE,
    `vocabulary_complexity` DOUBLE,
    `sentence_complexity` DOUBLE,
    `confidence_score` DOUBLE,
    `analysis_result` JSON,
    `processing_time` INT,
    `status` VARCHAR(20) NOT NULL DEFAULT 'SUCCESS',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted` BOOLEAN DEFAULT FALSE,
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_grade_level` (`grade_level`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 文本简化表
CREATE TABLE IF NOT EXISTS `text_simplification` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT NOT NULL,
    `analysis_id` BIGINT,
    `original_text` TEXT NOT NULL,
    `simplified_text` TEXT NOT NULL,
    `target_grade` VARCHAR(20) NOT NULL,
    `simplification_level` VARCHAR(20),
    `original_grade` VARCHAR(20),
    `simplified_grade` VARCHAR(20),
    `simplification_ratio` DOUBLE,
    `quality_score` DOUBLE,
    `processing_time` INT,
    `status` VARCHAR(20) NOT NULL DEFAULT 'SUCCESS',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted` BOOLEAN DEFAULT FALSE,
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_analysis_id` (`analysis_id`),
    INDEX `idx_target_grade` (`target_grade`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 阅读记录表
CREATE TABLE IF NOT EXISTS `reading_records` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT NOT NULL,
    `text_id` BIGINT,
    `text_title` VARCHAR(200),
    `text_content` TEXT,
    `reading_progress` DOUBLE DEFAULT 0,
    `reading_time` INT DEFAULT 0,
    `start_time` DATETIME,
    `end_time` DATETIME,
    `rating` INT,
    `notes` TEXT,
    `status` VARCHAR(20) NOT NULL DEFAULT 'READING',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted` BOOLEAN DEFAULT FALSE,
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_text_id` (`text_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 教学资源表
CREATE TABLE IF NOT EXISTS `teaching_resources` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT NOT NULL,
    `analysis_id` BIGINT,
    `resource_type` VARCHAR(50) NOT NULL,
    `title` VARCHAR(200) NOT NULL,
    `content` JSON NOT NULL,
    `target_grade` VARCHAR(20),
    `difficulty_level` INT,
    `tags` VARCHAR(500),
    `usage_count` INT DEFAULT 0,
    `rating` DOUBLE DEFAULT 0,
    `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted` BOOLEAN DEFAULT FALSE,
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_analysis_id` (`analysis_id`),
    INDEX `idx_resource_type` (`resource_type`),
    INDEX `idx_target_grade` (`target_grade`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
